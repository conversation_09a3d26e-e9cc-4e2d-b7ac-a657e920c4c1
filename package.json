{"name": "registration-form-component", "version": "1.0.1", "type": "module", "config": {"dsvPublicApiVersion": "1.0.6"}, "private": true, "dependencies": {"@hookform/resolvers": "^2.9.11", "@openreplay/tracker": "^16.2.1", "@radix-ui/react-accordion": "^1.2.3", "@radix-ui/react-checkbox": "^1.1.5", "@radix-ui/react-dialog": "^1.1.7", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.7", "@radix-ui/react-radio-group": "^1.2.4", "@radix-ui/react-select": "^2.1.7", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-tooltip": "^1.2.0", "@tailwindcss/vite": "^4.1.3", "@vitejs/plugin-react": "^4.3.4", "axios": "^1.7.9", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "dayjs": "^1.11.7", "html-react-parser": "^3.0.13", "i18next": "^22.4.13", "i18next-browser-languagedetector": "^7.0.1", "libphonenumber-js": "^1.10.37", "lucide-react": "^0.487.0", "react": "^18.2.0", "react-day-picker": "8.10.1", "react-dom": "^18.2.0", "react-hook-form": "^7.43.1", "react-i18next": "^12.2.0", "react-markdown": "^8.0.7", "react-router-dom": "^6.6.1", "tailwind-merge": "^3.2.0", "tailwindcss-animate": "^1.0.7", "tw-animate-css": "^1.2.5", "typescript": "^4.4.2", "vite": "^6.0.3", "vite-plugin-svgr": "^4.3.0", "vite-tsconfig-paths": "^5.1.3", "web-vitals": "^2.1.0", "zod": "^3.24.2", "zustand": "^5.0.3"}, "scripts": {"start": "vite", "mock": "VITE_API=mock vite", "local": "VITE_API=local vite", "dev": "VITE_API=dev vite", "build": "tsc && vite build", "preview": "vite preview", "test": "vitest", "test:coverage": "vitest run --coverage --watch=false", "test:ci": "CI=true vitest", "postinstall": "bash download-api.sh -v $npm_package_config_dsvPublicApiVersion && orval", "lint": "eslint \"src/**/*.{js,jsx,ts,tsx}\""}, "msw": {"workerDirectory": "public"}, "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@eslint/eslintrc": "^1.4.1", "@faker-js/faker": "^7.6.0", "@testing-library/dom": "^8.19.1", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^13.0.0", "@testing-library/user-event": "^13.2.1", "@types/node": "^16.18.101", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "@typescript-eslint/eslint-plugin": "^8.17.0", "@typescript-eslint/parser": "^8.17.0", "@vitest/coverage-c8": "^0.33.0", "@vitest/coverage-v8": "^2.1.8", "autoprefixer": "^10.4.21", "eslint": "^9.16.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.1.0", "eslint-plugin-testing-library": "^7.1.1", "jsdom": "^25.0.1", "msw": "^0.49.2", "openapi-types": "^12.1.0", "orval": "^6.11.0", "prettier": "^2.8.8", "sass": "^1.60.0", "tailwindcss": "^4.1.3", "vitest": "^2.1.8"}}