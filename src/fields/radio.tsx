import { EventRegistrationFormField } from 'src/generated/api/dsv-public/model';
import { useFormContext } from 'src/context/form-context';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { useMemo, useState } from 'react';
import { Label } from 'src/lib/components/ui/label';
import { RadioGroup, RadioGroupItem } from 'src/lib/components/ui/radio-group';

type RadioFieldProps = {
  field: EventRegistrationFormField;
  participantIndex: number;
};

export function RadioField({ field, participantIndex }: RadioFieldProps) {
  const [touched, setTouched] = useState<boolean>(false);
  const { updateFieldValue: updateParticipantField } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const currentValue = useMemo(() => {
    if (registrationEntries && registrationEntries.length > participantIndex) {
      return registrationEntries[participantIndex].fields[field.key]
        ? String(registrationEntries[participantIndex].fields[field.key])
        : undefined;
    }

    return undefined;
  }, [field.key, registrationEntries, participantIndex]);

  const handleChange = (value: string) => {
    setTouched(true);
    updateParticipantField(participantIndex, field.key, value);
  };

  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const error = invalidFields.find(invF => invF.key === `${participantIndex}-${field.key}`);
  const showError = (touched || forceErrorMessages) && error;

  return (
    <RadioGroup className="space-y-2" value={currentValue} onValueChange={handleChange}>
      <Label className="flex items-center">
        {field.name} {field.required && <span className="text-destructive ml-1">*</span>}
      </Label>
      <div className="space-y-2" id={`${participantIndex}-${field.key}`}>
        {field.values?.map((option: string) => (
          <div key={option} className="flex items-center space-x-2">
            <RadioGroupItem id={`${participantIndex}-${field.key}-${option}`} value={option} />
            <Label htmlFor={`${participantIndex}-${field.key}-${option}`} className="cursor-pointer">
              {option}
            </Label>
          </div>
        ))}
      </div>
      {showError && <p className="text-sm text-destructive">{error.message}</p>}
      {field.description && <p className="text-sm text-muted-foreground">{field.description}</p>}
    </RadioGroup>
  );
}
