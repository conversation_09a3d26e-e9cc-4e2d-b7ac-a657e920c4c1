import { EventRegistrationFormField } from 'src/generated/api/dsv-public/model';
import { Label } from 'src/lib/components/ui/label';
import { useFormContext } from 'src/context/form-context';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { ChangeEvent, useMemo, useState } from 'react';
import { Textarea } from 'src/lib/components/ui/textarea';

type MultiLineFieldProps = {
  field: EventRegistrationFormField;
  participantIndex: number;
};

export function MultiLineField({ field, participantIndex }: MultiLineFieldProps) {
  const [touched, setTouched] = useState<boolean>(false);
  const { updateFieldValue } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const currentValue = useMemo(() => {
    if (registrationEntries && registrationEntries.length > participantIndex) {
      return registrationEntries[participantIndex].fields[field.key]
        ? String(registrationEntries[participantIndex].fields[field.key])
        : undefined;
    }

    return undefined;
  }, [field.key, registrationEntries, participantIndex]);

  const handleChange = (e: ChangeEvent<HTMLTextAreaElement>) => {
    updateFieldValue(participantIndex, field.key, e.target.value);
  };

  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const error = invalidFields.find(invF => invF.key === `${participantIndex}-${field.key}`);
  const showError = (touched || forceErrorMessages) && error;

  return (
    <div className="space-y-2" key={field.key}>
      <Label htmlFor={`${participantIndex}-${field.key}`} className="flex items-center">
        {field.name} {field.required && <span className="text-destructive ml-1">*</span>}
      </Label>
      <Textarea
        id={`${participantIndex}-${field.key}`}
        value={currentValue}
        onChange={handleChange}
        onBlur={() => setTouched(true)}
        placeholder={field.placeholder}
        rows={field.options?.rows ? Number(field.options.rows) : 3}
        maxLength={field.maxLength}
        className={error ? 'border-destructive' : ''}
      />
      {showError && <p className="text-sm text-destructive">{error.message}</p>}
      {field.description && <p className="text-sm text-muted-foreground">{field.description}</p>}
    </div>
  );
}
