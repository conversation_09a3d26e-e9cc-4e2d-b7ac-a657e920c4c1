import { EventRegistrationFormField } from 'src/generated/api/dsv-public/model';
import { useFormContext } from 'src/context/form-context';
import { useFormProgressStore } from 'src/stores/form-progress-store';
import { useMemo, useState } from 'react';
import { Label } from 'src/lib/components/ui/label';
import MultipleSelector, { Option } from 'src/lib/components/ui/multiple-selector';

type MultiSelectFieldProps = {
  field: EventRegistrationFormField;
  participantIndex: number;
};

export function MultiSelectField({ field, participantIndex }: MultiSelectFieldProps) {
  const [touched, setTouched] = useState<boolean>(false);
  const { updateFieldValue: updateParticipantField } = useFormContext();
  const registrationEntries = useFormProgressStore(state => state.registrationEntries);
  const forceErrorMessages = useFormProgressStore(state => state.forceErrorMessages);
  const currentValue = useMemo(() => {
    const fieldValue = registrationEntries?.[participantIndex]?.fields?.[field.key] as string[] | undefined;
    if (!fieldValue) return [];
    return Array.isArray(fieldValue) ? fieldValue.map(v => ({ label: v, value: v })) : [fieldValue];
  }, [registrationEntries, participantIndex, field.key]);

  const handleChange = (value: Option[]) => {
    setTouched(true);
    updateParticipantField(
      participantIndex,
      field.key,
      value.map(v => v.value)
    );
  };

  const invalidFields = useFormProgressStore(state => state.invalidFields);
  const error = invalidFields.find(invF => invF.key === `${participantIndex}-${field.key}`);
  const showError = (touched || forceErrorMessages) && error;

  return (
    <div className="space-y-2" key={field.key}>
      <Label className="flex items-center">
        {field.name} {field.required && <span className="text-destructive ml-1">*</span>}
      </Label>
      <div className="space-y-2" id={`${participantIndex}-${field.key}`}>
        <MultipleSelector
          placeholder={field.placeholder}
          value={currentValue}
          options={field.values?.map(value => ({ value, label: value }))}
          onChange={handleChange}
        />
      </div>
      {showError && <p className="text-sm text-destructive">{error.message}</p>}
      {field.description && <p className="text-sm text-muted-foreground">{field.description}</p>}
    </div>
  );
}
